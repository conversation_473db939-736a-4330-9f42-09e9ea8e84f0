name: PathForge AI - Detect Changes

on:
  workflow_call:
    outputs:
      python:
        description: 'Python files changed'
        value: ${{ jobs.changes.outputs.python }}
      frontend:
        description: 'Frontend files changed'
        value: ${{ jobs.changes.outputs.frontend }}
      docker:
        description: 'Docker-related files changed'
        value: ${{ jobs.changes.outputs.docker }}
      agent_service:
        description: 'Agent service related files changed'
        value: ${{ jobs.changes.outputs.agent_service }}
      streamlit_app:
        description: 'Streamlit app related files changed'
        value: ${{ jobs.changes.outputs.streamlit_app }}
      frontend_docker:
        description: 'Frontend Docker build related files changed'
        value: ${{ jobs.changes.outputs.frontend_docker }}

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      python: ${{ steps.changes.outputs.python }}
      frontend: ${{ steps.changes.outputs.frontend }}
      docker: ${{ steps.changes.outputs.docker }}
      agent_service: ${{ steps.changes.outputs.agent_service }}
      streamlit_app: ${{ steps.changes.outputs.streamlit_app }}
      frontend_docker: ${{ steps.changes.outputs.frontend_docker }}
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Determine base for comparison
      id: base
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          # For workflow_dispatch, compare against the previous commit
          prev_commit=$(git rev-parse HEAD~1 2>/dev/null || git rev-parse HEAD)
          echo "base=$prev_commit" >> $GITHUB_OUTPUT
        elif [ "${{ github.event_name }}" = "push" ] && [[ "${{ github.ref }}" == refs/tags/* ]]; then
          # For tag pushes, compare against the previous commit
          prev_commit=$(git rev-parse HEAD~1 2>/dev/null || git rev-parse HEAD)
          echo "base=$prev_commit" >> $GITHUB_OUTPUT
        else
          # For other events, let paths-filter determine the base automatically
          echo "base=" >> $GITHUB_OUTPUT
        fi
    - name: Debug git information
      run: |
        echo "Event name: ${{ github.event_name }}"
        echo "Ref: ${{ github.ref }}"
        echo "Base for comparison: ${{ steps.base.outputs.base }}"
        echo "Recent commits:"
        git log --oneline -5
        echo "Changed files comparison:"
        if [ -n "${{ steps.base.outputs.base }}" ]; then
          git diff --name-only ${{ steps.base.outputs.base }} HEAD || echo "No changes detected"
        else
          echo "Using paths-filter default base detection"
        fi
    - uses: dorny/paths-filter@v3
      id: changes
      with:
        base: ${{ steps.base.outputs.base }}
        filters: |
          python:
            - '**/*.py'
          frontend:
            - 'src/frontend/**'
          docker:
            - '**/*.py'
            - '**/*.yml'
            - '**/*.yaml'
            - '**/Dockerfile*'
            - 'src/frontend/**'
          agent_service:
            - 'src/agents/**'
            - 'src/core/**'
            - 'src/memory/**'
            - 'src/schema/**'
            - 'src/service/**'
            - 'src/run_service.py'
            - 'docker/Dockerfile.service'
            - 'docker/start_service.py'
            - 'docker/service_init_patch.py'
            - 'docker/core_init_patch.py'
            - '.dockerignore.service'
            - 'requirements.txt'
            - 'pyproject.toml'
            - 'uv.lock'
          streamlit_app:
            - 'src/client/**'
            - 'src/schema/**'
            - 'src/streamlit_app.py'
            - 'docker/Dockerfile.app'
            - '.dockerignore.app'
            - 'requirements.txt'
            - 'pyproject.toml'
            - 'uv.lock'
          frontend_docker:
            - 'src/frontend/**'
            - 'docker/Dockerfile.frontend'
            - 'docker/nginx.conf'
            - '.dockerignore.frontend'
