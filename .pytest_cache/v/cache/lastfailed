{"tests/test_resume_retriever.py::TestResumeRetriever::test_build_query_with_filters": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_get_embedding_failure": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_get_embedding_success": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_get_relevant_documents_success": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_init_with_custom_parameters": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_init_without_api_key_raises_error": true, "tests/test_resume_retriever.py::TestResumeRetriever::test_update_filters": true, "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_project_retriever": true, "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_skill_retriever": true, "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_user_retriever": true, "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_work_experience_retriever": true, "src/agents/resume_retriever.py": true}