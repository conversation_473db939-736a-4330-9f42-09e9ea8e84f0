["tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_project_retriever", "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_skill_retriever", "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_user_retriever", "tests/test_resume_retriever.py::TestConvenienceFunctions::test_create_work_experience_retriever", "tests/test_resume_retriever.py::TestResumeRetriever::test_build_query_basic", "tests/test_resume_retriever.py::TestResumeRetriever::test_build_query_with_filters", "tests/test_resume_retriever.py::TestResumeRetriever::test_get_embedding_failure", "tests/test_resume_retriever.py::TestResumeRetriever::test_get_embedding_success", "tests/test_resume_retriever.py::TestResumeRetriever::test_get_relevant_documents_success", "tests/test_resume_retriever.py::TestResumeRetriever::test_init_with_custom_parameters", "tests/test_resume_retriever.py::TestResumeRetriever::test_init_with_defaults", "tests/test_resume_retriever.py::TestResumeRetriever::test_init_without_api_key_raises_error", "tests/test_resume_retriever.py::TestResumeRetriever::test_update_filters"]