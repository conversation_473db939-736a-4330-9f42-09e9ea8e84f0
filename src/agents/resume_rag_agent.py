"""
Resume RAG Agent - Integration example for ResumeRetriever with LangChain agents.

This module demonstrates how to integrate the ResumeRetriever with LangChain
agents and chains to create a conversational AI that can answer questions
about people's skills, experience, and projects.
"""

import logging
from typing import Dict, Any, List, Optional

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser
from langchain.tools.retriever import create_retriever_tool
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from agents.resume_retriever import (
    ResumeRetriever,
    create_skill_retriever,
    create_work_experience_retriever,
    create_project_retriever,
    create_user_retriever
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ResumeRAGAgent:
    """
    Resume RAG Agent that combines semantic retrieval with conversational AI.
    
    This agent can answer questions about people's skills, work experience,
    and projects by retrieving relevant information from the resume database
    and generating contextual responses.
    """

    def __init__(
        self,
        model_name: str = "gpt-4.1-mini",
        model_provider: str = "openai",
        retriever_k: int = 5,
        similarity_threshold: float = 0.0
    ):
        """
        Initialize the Resume RAG Agent.
        
        Args:
            model_name: Name of the language model to use
            model_provider: Provider of the language model
            retriever_k: Number of documents to retrieve
            similarity_threshold: Minimum similarity score for retrieval
        """
        self.model_name = model_name
        self.model_provider = model_provider
        self.retriever_k = retriever_k
        self.similarity_threshold = similarity_threshold

        # Initialize the language model
        self.llm = init_chat_model(model_name, model_provider=model_provider)

        # Initialize the general retriever
        self.general_retriever = ResumeRetriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create specialized retrievers
        self.skill_retriever = create_skill_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.work_retriever = create_work_experience_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.project_retriever = create_project_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create retriever tools for agent use
        self.retriever_tools = self._create_retriever_tools()

        # Initialize the RAG chain
        self.rag_chain = self._create_rag_chain()

        # Initialize the agent
        self.agent = self._create_agent()

    def _create_retriever_tools(self) -> List:
        """Create LangChain tools from retrievers."""
        tools = []

        # General resume search tool
        general_tool = create_retriever_tool(
            self.general_retriever,
            "search_resumes",
            "Search through all resume data including skills, work experience, and projects. "
            "Use this for general queries about people's backgrounds."
        )
        tools.append(general_tool)

        # Skill-specific search tool
        skill_tool = create_retriever_tool(
            self.skill_retriever,
            "search_skills",
            "Search specifically for skills and technical expertise. "
            "Use this when looking for people with specific technical skills or experience levels."
        )
        tools.append(skill_tool)

        # Work experience search tool
        work_tool = create_retriever_tool(
            self.work_retriever,
            "search_work_experience",
            "Search through work experience and job history. "
            "Use this when looking for people who worked at specific companies or in specific roles."
        )
        tools.append(work_tool)

        # Project search tool
        project_tool = create_retriever_tool(
            self.project_retriever,
            "search_projects",
            "Search through project portfolios and achievements. "
            "Use this when looking for people who worked on specific types of projects."
        )
        tools.append(project_tool)

        return tools

    def _create_rag_chain(self):
        """Create a RAG chain for simple question-answering."""

        # Define the prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful HR assistant that can answer questions about people's skills, work experience, and projects based on resume data.

Use the provided context to answer questions accurately and comprehensively. If the context doesn't contain enough information to answer the question, say so clearly.

When mentioning people, always include their full name and relevant details from their resume.

Context:
{context}"""),
            ("human", "{question}")
        ])

        # Define the chain
        def format_docs(docs):
            """Format retrieved documents for the prompt."""
            if not docs:
                return "No relevant information found."

            formatted = []
            for doc in docs:
                metadata = doc.metadata
                formatted.append(
                    f"Person: {metadata.get('full_name', 'Unknown')}\n"
                    f"Type: {metadata.get('chunk_type', 'Unknown')}\n"
                    f"Content: {doc.page_content}\n"
                    f"Similarity Score: {metadata.get('similarity_score', 0):.3f}\n"
                )
            return "\n---\n".join(formatted)

        chain = (
            {"context": self.general_retriever | format_docs, "question": RunnablePassthrough()}
            | prompt
            | self.llm
            | StrOutputParser()
        )

        return chain

    def _create_agent(self):
        """Create a ReAct agent with retriever tools."""
        memory = MemorySaver()

        # System message for the agent
        system_message = """You are a helpful HR assistant that can search through resume data to answer questions about people's skills, work experience, and projects.

        You have access to several search tools:
        - search_resumes: For general queries about people's backgrounds
        - search_skills: For finding people with specific technical skills
        - search_work_experience: For finding people who worked at specific companies or roles
        - search_projects: For finding people who worked on specific types of projects
        
        Always use the most appropriate tool for each query. When presenting results, include the person's name and relevant details.
        
        If you can't find relevant information, suggest alternative search terms or approaches."""

        agent = create_react_agent(
            self.llm,
            self.retriever_tools,
            checkpointer=memory,
            state_modifier=system_message
        )

        return agent

    def query_simple(self, question: str) -> str:
        """
        Answer a question using the simple RAG chain.
        
        Args:
            question: The question to answer
            
        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing simple query: {question}")
            response = self.rag_chain.invoke(question)
            return response
        except Exception as e:
            logger.error(f"Error in simple query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"

    def query_agent(self, question: str, thread_id: str = "default") -> str:
        """
        Answer a question using the ReAct agent.
        
        Args:
            question: The question to answer
            thread_id: Thread ID for conversation memory
            
        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing agent query: {question}")
            config = {"configurable": {"thread_id": thread_id}}

            response = self.agent.invoke(
                {"messages": [HumanMessage(content=question)]},
                config=config
            )

            # Extract the final message content
            if response and "messages" in response:
                final_message = response["messages"][-1]
                if hasattr(final_message, 'content'):
                    return final_message.content

            return "I couldn't generate a proper response."

        except Exception as e:
            logger.error(f"Error in agent query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"

    def search_user_profile(self, user_id: str, query: str = "all information") -> str:
        """
        Search for information about a specific user.
        
        Args:
            user_id: The user ID to search for
            query: Specific query about the user
            
        Returns:
            Formatted information about the user
        """
        try:
            user_retriever = create_user_retriever(user_id=user_id, k=20)
            docs = user_retriever.invoke(query)

            if not docs:
                return f"No information found for user ID: {user_id}"

            # Group documents by type
            skills = []
            work_exp = []
            projects = []
            other = []

            user_name = "Unknown"
            user_email = "Unknown"

            for doc in docs:
                metadata = doc.metadata
                chunk_type = metadata.get('chunk_type', 'unknown')

                # Extract user info from first document
                if user_name == "Unknown":
                    user_name = metadata.get('full_name', 'Unknown')
                    user_email = metadata.get('email', 'Unknown')

                if chunk_type == 'skill_group':
                    skills.append(doc.page_content)
                elif chunk_type == 'work_experience':
                    work_exp.append(doc.page_content)
                elif chunk_type == 'project':
                    projects.append(doc.page_content)
                else:
                    other.append(doc.page_content)

            # Format the response
            response = f"Profile for {user_name} ({user_email})\n"
            response += "=" * 50 + "\n\n"

            if skills:
                response += "SKILLS:\n"
                for skill in skills:
                    response += f"• {skill}\n"
                response += "\n"

            if work_exp:
                response += "WORK EXPERIENCE:\n"
                for exp in work_exp:
                    response += f"• {exp}\n"
                response += "\n"

            if projects:
                response += "PROJECTS:\n"
                for project in projects:
                    response += f"• {project}\n"
                response += "\n"

            if other:
                response += "OTHER:\n"
                for item in other:
                    response += f"• {item}\n"

            return response

        except Exception as e:
            logger.error(f"Error searching user profile: {str(e)}")
            return f"Error retrieving profile for user {user_id}: {str(e)}"


# Example usage and testing functions

def demo_resume_rag_agent():
    """Demonstrate the Resume RAG Agent functionality."""
    print("🚀 Resume RAG Agent Demo")
    print("=" * 50)

    try:
        # Initialize the agent
        agent = ResumeRAGAgent()

        # Example queries
        queries = [
            "Who has Python programming skills?",
            "Find people with machine learning experience",
            "Show me software engineers who worked at tech companies",
            "What projects involve data science or AI?",
            "Who has 5-10 years of experience in backend development?"
        ]

        print("\n🤖 Testing Simple RAG Chain:")
        for i, query in enumerate(queries[:2], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_simple(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n🤖 Testing ReAct Agent:")
        for i, query in enumerate(queries[2:4], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_agent(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n✅ Demo completed successfully!")

    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


# LangGraph-compatible wrapper for integration with agents.py
def create_resume_rag_graph():
    """
    Create a LangGraph Pregel object for the Resume RAG Agent.

    This function creates a LangGraph-compatible agent that can be integrated
    into the main agent system in agents.py.

    Returns:
        Pregel: A compiled LangGraph agent
    """
    from typing import TypedDict, Annotated
    from langgraph.graph import StateGraph, add_messages, START, END
    from langgraph.checkpoint.memory import MemorySaver
    from langchain_core.messages import AIMessage

    class ResumeRAGState(TypedDict):
        """State for Resume RAG agent."""
        messages: Annotated[list, add_messages]

    # Initialize the Resume RAG Agent instance
    resume_agent = ResumeRAGAgent()

    def resume_rag_node(state: ResumeRAGState) -> ResumeRAGState:
        """
        Process user messages using the Resume RAG Agent.

        Args:
            state: Current state containing messages

        Returns:
            Updated state with agent response
        """
        messages = state["messages"]
        if not messages:
            return {"messages": [AIMessage(content="Hello! I can help you search through resume data. What would you like to know?")]}

        # Get the last human message
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            user_query = last_message.content
        else:
            user_query = str(last_message)

        try:
            # Use the agent's query method to get response
            response = resume_agent.query_agent(user_query)
            return {"messages": [AIMessage(content=response)]}
        except Exception as e:
            logger.error(f"Error in resume RAG node: {str(e)}")
            return {"messages": [AIMessage(content=f"Sorry, I encountered an error: {str(e)}")]}

    # Build the graph
    graph_builder = StateGraph(ResumeRAGState)
    graph_builder.add_node("resume_rag", resume_rag_node)
    graph_builder.add_edge(START, "resume_rag")
    graph_builder.add_edge("resume_rag", END)

    # Compile with memory
    memory = MemorySaver()
    return graph_builder.compile(checkpointer=memory)


# Create the graph instance for export
resume_rag_agent = create_resume_rag_graph()


if __name__ == "__main__":
    demo_resume_rag_agent()
